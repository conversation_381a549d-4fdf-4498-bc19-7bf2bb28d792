{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/SearchModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X, Search, TrendingUp } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface SearchModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst SearchModal = ({ isOpen, onClose }: SearchModalProps) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<any[]>([]);\n\n  // Mock search data\n  const trendingSearches = [\n    'Gucci bags',\n    'Saint Laurent shoes',\n    'The Row coats',\n    'Bottega Veneta',\n    'K<PERSON>te dresses',\n    'Designer jeans',\n    'Luxury sneakers',\n    'Evening dresses'\n  ];\n\n  const mockProducts = [\n    { id: 1, name: 'Ophidia GG Supreme bag', brand: 'Gucci', price: 1200, category: 'Bags' },\n    { id: 2, name: 'Opyum pumps', brand: 'Saint Laurent', price: 895, category: 'Shoes' },\n    { id: 3, name: 'Cashmere coat', brand: 'The Row', price: 2890, category: 'Coats' },\n    { id: 4, name: 'Intrecciato wallet', brand: 'Bottega Veneta', price: 450, category: 'Accessories' },\n  ];\n\n  useEffect(() => {\n    if (searchQuery.length > 2) {\n      // Simulate search results\n      const filtered = mockProducts.filter(product =>\n        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.category.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setSearchResults(filtered);\n    } else {\n      setSearchResults([]);\n    }\n  }, [searchQuery]);\n\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 bg-white\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-4\">\n        <div className=\"max-w-4xl mx-auto flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\n            <input\n              type=\"text\"\n              placeholder=\"Search for designers, products, or categories...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-12 pr-4 py-3 text-lg border-none outline-none\"\n              autoFocus\n            />\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-4xl mx-auto p-4\">\n        {searchQuery.length === 0 ? (\n          /* Trending Searches */\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n              <TrendingUp size={20} className=\"mr-2\" />\n              Trending Searches\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              {trendingSearches.map((search, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSearchQuery(search)}\n                  className=\"text-left p-3 border border-gray-200 hover:border-black transition-colors text-sm\"\n                >\n                  {search}\n                </button>\n              ))}\n            </div>\n\n            {/* Popular Categories */}\n            <div className=\"mt-8\">\n              <h3 className=\"text-lg font-semibold mb-4\">Popular Categories</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <Link href=\"/womens/shop/clothing/dresses\" className=\"group\">\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Dresses</span>\n                  </div>\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Women's Dresses</h4>\n                </Link>\n                <Link href=\"/womens/shop/bags\" className=\"group\">\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Bags</span>\n                  </div>\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Designer Bags</h4>\n                </Link>\n                <Link href=\"/mens/shop/clothing/suits\" className=\"group\">\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Suits</span>\n                  </div>\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Men's Suits</h4>\n                </Link>\n              </div>\n            </div>\n          </div>\n        ) : (\n          /* Search Results */\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">\n              Search Results for \"{searchQuery}\" ({searchResults.length})\n            </h3>\n            {searchResults.length > 0 ? (\n              <div className=\"space-y-4\">\n                {searchResults.map((product) => (\n                  <Link\n                    key={product.id}\n                    href={`/product/${product.id}`}\n                    className=\"flex items-center space-x-4 p-4 hover:bg-gray-50 transition-colors\"\n                    onClick={onClose}\n                  >\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\n                      <span className=\"text-xs text-gray-500\">IMG</span>\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-xs text-gray-500 uppercase tracking-wide\">{product.brand}</p>\n                      <h4 className=\"font-medium\">{product.name}</h4>\n                      <p className=\"text-sm text-gray-600\">£{product.price}</p>\n                    </div>\n                    <div className=\"text-xs text-gray-500\">{product.category}</div>\n                  </Link>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <p className=\"text-gray-500 mb-4\">No results found for \"{searchQuery}\"</p>\n                <p className=\"text-sm text-gray-400\">Try searching for designers, product names, or categories</p>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SearchModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAWA,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,OAAO,EAAoB;;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE5D,mBAAmB;IACnB,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;YAAE,IAAI;YAAG,MAAM;YAA0B,OAAO;YAAS,OAAO;YAAM,UAAU;QAAO;QACvF;YAAE,IAAI;YAAG,MAAM;YAAe,OAAO;YAAiB,OAAO;YAAK,UAAU;QAAQ;QACpF;YAAE,IAAI;YAAG,MAAM;YAAiB,OAAO;YAAW,OAAO;YAAM,UAAU;QAAQ;QACjF;YAAE,IAAI;YAAG,MAAM;YAAsB,OAAO;YAAkB,OAAO;YAAK,UAAU;QAAc;KACnG;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,0BAA0B;gBAC1B,MAAM,WAAW,aAAa,MAAM;sDAAC,CAAA,UACnC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;gBAEjE,iBAAiB;YACnB,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF;gCAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;yCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;gCAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAmE,MAAM;;;;;;8CAC3F,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,SAAS;;;;;;;;;;;;sCAGb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,IACtB,qBAAqB,iBACrB,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAET;mCAJI;;;;;;;;;;sCAUX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;;8DACnD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;sDAExD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;;8DACvC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;sDAExD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA4B,WAAU;;8DAC/C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAM9D,kBAAkB,iBAClB,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;;gCAA6B;gCACpB;gCAAY;gCAAI,cAAc,MAAM;gCAAC;;;;;;;wBAE3D,cAAc,MAAM,GAAG,kBACtB,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;oCAC9B,WAAU;oCACV,SAAS;;sDAET,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAE1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAiD,QAAQ,KAAK;;;;;;8DAC3E,6LAAC;oDAAG,WAAU;8DAAe,QAAQ,IAAI;;;;;;8DACzC,6LAAC;oDAAE,WAAU;;wDAAwB;wDAAE,QAAQ,KAAK;;;;;;;;;;;;;sDAEtD,6LAAC;4CAAI,WAAU;sDAAyB,QAAQ,QAAQ;;;;;;;mCAbnD,QAAQ,EAAE;;;;;;;;;iDAkBrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAqB;wCAAuB;wCAAY;;;;;;;8CACrE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhKM;KAAA;uCAkKS", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport <PERSON> from 'next/link';\nimport { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';\nimport MegaMenu from './MegaMenu';\nimport SearchModal from './SearchModal';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeMenu, setActiveMenu] = useState<string | null>(null);\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  const handleMenuHover = (menu: string) => {\n    setActiveMenu(menu);\n  };\n\n  const handleMenuLeave = () => {\n    setActiveMenu(null);\n  };\n\n  return (\n    <header className=\"relative bg-white border-b border-gray-200\">\n      {/* Top Bar - Hidden on mobile */}\n      <div className=\"hidden md:block bg-black text-white text-xs py-2\">\n        <div className=\"max-w-7xl mx-auto px-4 flex justify-between items-center\">\n          <div className=\"flex space-x-6\">\n            <Link href=\"/help\" className=\"hover:underline\">Help Centre</Link>\n            <Link href=\"/delivery\" className=\"hover:underline\">Delivery</Link>\n            <Link href=\"/returns\" className=\"hover:underline\">Returns</Link>\n          </div>\n          <div className=\"flex space-x-6\">\n            <Link href=\"/stores\" className=\"hover:underline\">Visit Us</Link>\n            <Link href=\"/apps\" className=\"hover:underline\">Our Apps</Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div className=\"max-w-7xl mx-auto px-4 relative\">\n        <div className=\"flex items-center h-16 md:h-20\">\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2 mr-4\"\n            onClick={toggleMobileMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex-shrink-0 mr-8\">\n            <span className=\"text-2xl md:text-3xl font-bold tracking-wider\">MATCHES</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center flex-1\">\n            {/* Primary Categories - Men & Women */}\n            <div className=\"flex space-x-8 mr-12\">\n              <div\n                onMouseEnter={() => handleMenuHover('women')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/womens\"\n                  className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Women\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('men')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/mens\"\n                  className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Men\n                </Link>\n              </div>\n            </div>\n\n            {/* Secondary Categories */}\n            <div className=\"flex space-x-6 flex-1\">\n              <div\n                onMouseEnter={() => handleMenuHover('just-in')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/just-in\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Just In\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('designers')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/designers\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Designers\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('clothing')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/clothing\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Clothing\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('dresses')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/dresses\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Dresses\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('shoes')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/shoes\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Shoes\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('bags')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/bags\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Bags\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('accessories')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/accessories\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Accessories\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('jewellery')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/jewellery-watches\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Jewellery & Watches\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('home')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/home\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Home\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('edits')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/edits\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Edits\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('outlet')}\n                onMouseLeave={handleMenuLeave}\n              >\n                <Link\n                  href=\"/outlet\"\n                  className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Outlet\n                </Link>\n              </div>\n            </div>\n          </nav>\n\n          {/* Right Icons */}\n          <div className=\"flex items-center space-x-4 ml-auto\">\n            <button\n              className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n              onClick={() => setIsSearchOpen(true)}\n            >\n              <Search size={20} />\n            </button>\n            <button className=\"hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <User size={20} />\n            </button>\n            <button className=\"hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <Settings size={20} />\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors relative\">\n              <Heart size={20} />\n              <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                0\n              </span>\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors relative\">\n              <ShoppingBag size={20} />\n              <span className=\"absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                0\n              </span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu Overlay */}\n      {isMobileMenuOpen && (\n        <div className=\"fixed inset-0 z-50 md:hidden\">\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" onClick={toggleMobileMenu} />\n          <div className=\"fixed left-0 top-0 h-full w-80 bg-white shadow-xl\">\n            <div className=\"flex items-center justify-between p-4 border-b\">\n              <span className=\"text-xl font-bold\">MATCHES</span>\n              <button onClick={toggleMobileMenu}>\n                <X size={24} />\n              </button>\n            </div>\n            <nav className=\"p-4 overflow-y-auto\">\n              <div className=\"space-y-4\">\n                {/* Primary Categories */}\n                <div className=\"space-y-2\">\n                  <Link\n                    href=\"/womens\"\n                    className=\"block text-lg font-medium py-2 border-b border-gray-100\"\n                    onClick={toggleMobileMenu}\n                  >\n                    Women\n                  </Link>\n                  <Link\n                    href=\"/mens\"\n                    className=\"block text-lg font-medium py-2 border-b border-gray-100\"\n                    onClick={toggleMobileMenu}\n                  >\n                    Men\n                  </Link>\n                </div>\n\n                {/* Secondary Categories */}\n                <div className=\"pt-4 space-y-2\">\n                  <h3 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2\">Shop</h3>\n                  <Link href=\"/just-in\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Just In</Link>\n                  <Link href=\"/designers\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Designers</Link>\n                  <Link href=\"/clothing\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Clothing</Link>\n                  <Link href=\"/dresses\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Dresses</Link>\n                  <Link href=\"/shoes\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Shoes</Link>\n                  <Link href=\"/bags\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Bags</Link>\n                  <Link href=\"/accessories\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Accessories</Link>\n                  <Link href=\"/jewellery-watches\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Jewellery & Watches</Link>\n                  <Link href=\"/home\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Home</Link>\n                  <Link href=\"/edits\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Edits</Link>\n                  <Link href=\"/outlet\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Outlet</Link>\n                </div>\n\n                {/* Account & Support */}\n                <div className=\"pt-4 space-y-2 border-t border-gray-200\">\n                  <h3 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2\">Account</h3>\n                  <Link href=\"/account\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>My Account</Link>\n                  <Link href=\"/settings\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Settings</Link>\n                  <Link href=\"/help\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Help Centre</Link>\n                  <Link href=\"/delivery\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Delivery</Link>\n                </div>\n              </div>\n            </nav>\n          </div>\n        </div>\n      )}\n\n      {/* Search Modal */}\n      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AANA;;;;;AAQA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;8CAC/C,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAkB;;;;;;8CACnD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAkB;;;;;;;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAkB;;;;;;8CACjD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,iCAAmB,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;sCAIpD,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC;gCAAK,WAAU;0CAAgD;;;;;;;;;;;sCAIlE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;4CACC,cAAc,IAAM,gBAAgB;4CACpC,cAAc;sDAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,gBAAgB;8CAE/B,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;8CAEhB,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;;8CAElB,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAA+G;;;;;;;;;;;;8CAIjI,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASpI,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAuC,SAAS;;;;;;kCAC/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,6LAAC;wCAAO,SAAS;kDACf,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;sDAMH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmE;;;;;;8DACjF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACtG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACrG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAClG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAe,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACxG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAC9G,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAClG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAU,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;;;;;;;sDAIrG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmE;;;;;;8DACjF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACrG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjH,6LAAC,oIAAA,CAAA,UAAW;gBAAC,QAAQ;gBAAc,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAGxE;GA5SM;KAAA;uCA8SS", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Instagram, Facebook, Youtube, Twitter } from 'lucide-react';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-white border-t border-gray-200\">\n      {/* Newsletter Section */}\n      <div className=\"bg-gray-50 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Stay in the know</h2>\n          <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\n            Be the first to discover new arrivals, exclusive collections, and styling tips\n          </p>\n          <div className=\"flex max-w-md mx-auto\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email address\"\n              className=\"flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:border-black\"\n            />\n            <button className=\"luxury-button\">\n              Sign up\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* MATCHES */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">MATCHES</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/bio\" className=\"text-sm text-gray-600 hover:text-black\">About Us</Link></li>\n              <li><Link href=\"/careers\" className=\"text-sm text-gray-600 hover:text-black\">Careers</Link></li>\n              <li><Link href=\"/affiliates\" className=\"text-sm text-gray-600 hover:text-black\">Affiliates</Link></li>\n              <li><Link href=\"/press\" className=\"text-sm text-gray-600 hover:text-black\">Press</Link></li>\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Services</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/private-shopping\" className=\"text-sm text-gray-600 hover:text-black\">Private Shopping</Link></li>\n              <li><Link href=\"/loyalty\" className=\"text-sm text-gray-600 hover:text-black\">Loyalty</Link></li>\n              <li><Link href=\"/rental\" className=\"text-sm text-gray-600 hover:text-black\">MATCHES Rental</Link></li>\n              <li><Link href=\"/gift-cards\" className=\"text-sm text-gray-600 hover:text-black\">Gift Cards</Link></li>\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/terms\" className=\"text-sm text-gray-600 hover:text-black\">Terms and Conditions</Link></li>\n              <li><Link href=\"/privacy\" className=\"text-sm text-gray-600 hover:text-black\">Privacy Policy</Link></li>\n              <li><Link href=\"/cookies\" className=\"text-sm text-gray-600 hover:text-black\">Cookie Policy</Link></li>\n              <li><Link href=\"/modern-slavery\" className=\"text-sm text-gray-600 hover:text-black\">Modern Slavery Statement</Link></li>\n            </ul>\n          </div>\n\n          {/* Visit Us */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Visit Us</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/stores/5carlosplace\" className=\"text-sm text-gray-600 hover:text-black\">5 Carlos Place</Link></li>\n              <li><Link href=\"/stores/marylebone\" className=\"text-sm text-gray-600 hover:text-black\">Marylebone</Link></li>\n              <li><Link href=\"/stores/wimbledon\" className=\"text-sm text-gray-600 hover:text-black\">Wimbledon</Link></li>\n            </ul>\n          </div>\n\n          {/* Help Centre */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Help Centre</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/help\" className=\"text-sm text-gray-600 hover:text-black\">Help Centre</Link></li>\n              <li><Link href=\"/returns\" className=\"text-sm text-gray-600 hover:text-black\">Returning an item</Link></li>\n              <li><Link href=\"/delivery\" className=\"text-sm text-gray-600 hover:text-black\">Delivery</Link></li>\n              <li><Link href=\"/size-guide\" className=\"text-sm text-gray-600 hover:text-black\">Size Guide</Link></li>\n            </ul>\n          </div>\n\n          {/* Social Media & Apps */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Follow Us</h3>\n            <div className=\"flex space-x-4 mb-6\">\n              <Link href=\"https://instagram.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Instagram size={20} />\n              </Link>\n              <Link href=\"https://facebook.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Facebook size={20} />\n              </Link>\n              <Link href=\"https://youtube.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Youtube size={20} />\n              </Link>\n              <Link href=\"https://twitter.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Twitter size={20} />\n              </Link>\n            </div>\n            <h4 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Our Apps</h4>\n            <div className=\"space-y-2\">\n              <Link href=\"#\" className=\"block\">\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\n                  Download on the App Store\n                </div>\n              </Link>\n              <Link href=\"#\" className=\"block\">\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\n                  Get it on Google Play\n                </div>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-200 py-6\">\n        <div className=\"max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\n            <span className=\"text-sm text-gray-600\">Shipping to</span>\n            <button className=\"text-sm font-medium border border-gray-300 px-3 py-1 hover:border-black\">\n              United Kingdom\n            </button>\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            © Copyright 2024 MATCHES\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAGnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAyC;;;;;;;;;;;sDACzE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;sDAChF,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK/E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;sDACtF,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAyC;;;;;;;;;;;sDAC5E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKxF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAAyC;;;;;;;;;;;sDACzF,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAyC;;;;;;;;;;;sDACvF,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK1F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAyC;;;;;;;;;;;sDAC1E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAyC;;;;;;;;;;;sDAC9E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;sDACnD,cAAA,6LAAC,+MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA+B,WAAU;sDAClD,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;sDAElB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;sDAEjB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;sDAIjE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC;oCAAO,WAAU;8CAA0E;;;;;;;;;;;;sCAI9F,6LAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KAlIM;uCAoIS", "debugId": null}}, {"offset": {"line": 2027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/QCTestingPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Monitor, Smartphone, Tablet, Eye, X } from 'lucide-react';\n\nconst QCTestingPanel = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');\n\n  const breakpoints = [\n    { id: 'mobile', name: 'Mobile', width: '375px', icon: Smartphone },\n    { id: 'tablet', name: 'Tablet', width: '768px', icon: Tablet },\n    { id: 'desktop', name: 'Desktop', width: '1920px', icon: Monitor },\n  ];\n\n  const testPages = [\n    { name: 'Homepage', path: '/' },\n    { name: 'Women\\'s Landing', path: '/womens' },\n    { name: 'Women\\'s Clothing', path: '/womens/shop/clothing' },\n    { name: 'Men\\'s Landing', path: '/mens' },\n    { name: 'Men\\'s Clothing', path: '/mens/shop/clothing' },\n  ];\n\n  const qcChecklist = [\n    'Header navigation is properly aligned',\n    'Logo is clearly visible and properly sized',\n    'Mega menus display correctly on hover',\n    'Product grids maintain proper spacing',\n    'Typography hierarchy is consistent',\n    'Interactive elements have proper hover states',\n    'Footer content is well-organized',\n    'Mobile navigation works smoothly',\n    'Search functionality is accessible',\n    'Product cards display correctly',\n  ];\n\n  if (!isOpen) {\n    return (\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg hover:bg-gray-800 transition-colors z-50\"\n        title=\"Open QC Testing Panel\"\n      >\n        <Eye size={20} />\n      </button>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-bold\">QC Testing Panel - Luxury Fashion Standards</h2>\n          <button\n            onClick={() => setIsOpen(false)}\n            className=\"p-2 hover:bg-gray-100 rounded-full\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-6\">\n          {/* Breakpoint Testing */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Responsive Breakpoint Testing</h3>\n            <div className=\"grid grid-cols-3 gap-4 mb-4\">\n              {breakpoints.map((bp) => {\n                const IconComponent = bp.icon;\n                return (\n                  <button\n                    key={bp.id}\n                    onClick={() => setCurrentBreakpoint(bp.id)}\n                    className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${\n                      currentBreakpoint === bp.id\n                        ? 'border-black bg-gray-50'\n                        : 'border-gray-200 hover:border-gray-400'\n                    }`}\n                  >\n                    <IconComponent size={24} />\n                    <span className=\"font-medium\">{bp.name}</span>\n                    <span className=\"text-sm text-gray-500\">{bp.width}</span>\n                  </button>\n                );\n              })}\n            </div>\n            <p className=\"text-sm text-gray-600\">\n              Current testing viewport: <strong>{breakpoints.find(bp => bp.id === currentBreakpoint)?.width}</strong>\n            </p>\n          </div>\n\n          {/* Page Testing */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Page Testing</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              {testPages.map((page, index) => (\n                <a\n                  key={index}\n                  href={page.path}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-3 border border-gray-200 rounded hover:border-black transition-colors text-sm\"\n                >\n                  {page.name} →\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* QC Checklist */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Luxury Fashion QC Checklist</h3>\n            <div className=\"space-y-2\">\n              {qcChecklist.map((item, index) => (\n                <label key={index} className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    className=\"w-4 h-4 text-black border-gray-300 rounded focus:ring-black\"\n                  />\n                  <span className=\"text-sm\">{item}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Design Standards */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Luxury Fashion Design Standards</h3>\n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <h4 className=\"font-medium mb-2\">Typography</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• Clean, geometric sans-serif fonts</li>\n                    <li>• Consistent hierarchy and spacing</li>\n                    <li>• Uppercase tracking for headings</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2\">Layout</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• No rounded corners (clean geometric design)</li>\n                    <li>• Sophisticated spacing hierarchy</li>\n                    <li>• Pixel-perfect alignment</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2\">Interactions</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• Smooth hover transitions</li>\n                    <li>• Luxury micro-interactions</li>\n                    <li>• Responsive touch targets</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2\">Content</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• High-quality product imagery</li>\n                    <li>• Rich mega menu layouts</li>\n                    <li>• Editorial content integration</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Testing Instructions */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Testing Instructions</h3>\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <ol className=\"list-decimal list-inside space-y-2 text-sm\">\n                <li>Test each page at all three breakpoints (375px, 768px, 1920px)</li>\n                <li>Verify navigation functionality and mega menu interactions</li>\n                <li>Check product grid layouts and card hover states</li>\n                <li>Ensure search modal opens and functions correctly</li>\n                <li>Validate footer content organization and links</li>\n                <li>Test mobile navigation and touch interactions</li>\n                <li>Verify pixel-perfect alignment and spacing</li>\n                <li>Check for luxury fashion aesthetic consistency</li>\n              </ol>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QCTestingPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc;QAClB;YAAE,IAAI;YAAU,MAAM;YAAU,OAAO;YAAS,MAAM,iNAAA,CAAA,aAAU;QAAC;QACjE;YAAE,IAAI;YAAU,MAAM;YAAU,OAAO;YAAS,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC7D;YAAE,IAAI;YAAW,MAAM;YAAW,OAAO;YAAU,MAAM,2MAAA,CAAA,UAAO;QAAC;KAClE;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAoB,MAAM;QAAU;QAC5C;YAAE,MAAM;YAAqB,MAAM;QAAwB;QAC3D;YAAE,MAAM;YAAkB,MAAM;QAAQ;QACxC;YAAE,MAAM;YAAmB,MAAM;QAAsB;KACxD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAU;YACV,OAAM;sBAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;;;;;;IAGjB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,6LAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,gBAAgB,GAAG,IAAI;wCAC7B,qBACE,6LAAC;4CAEC,SAAS,IAAM,qBAAqB,GAAG,EAAE;4CACzC,WAAW,CAAC,6EAA6E,EACvF,sBAAsB,GAAG,EAAE,GACvB,4BACA,yCACJ;;8DAEF,6LAAC;oDAAc,MAAM;;;;;;8DACrB,6LAAC;oDAAK,WAAU;8DAAe,GAAG,IAAI;;;;;;8DACtC,6LAAC;oDAAK,WAAU;8DAAyB,GAAG,KAAK;;;;;;;2CAV5C,GAAG,EAAE;;;;;oCAahB;;;;;;8CAEF,6LAAC;oCAAE,WAAU;;wCAAwB;sDACT,6LAAC;sDAAQ,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,oBAAoB;;;;;;;;;;;;;;;;;;sCAK5F,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,QAAO;4CACP,KAAI;4CACJ,WAAU;;gDAET,KAAK,IAAI;gDAAC;;2CANN;;;;;;;;;;;;;;;;sCAab,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDACC,MAAK;oDACL,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;2CALjB;;;;;;;;;;;;;;;;sCAYlB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAtLM;KAAA;uCAwLS", "debugId": null}}]}