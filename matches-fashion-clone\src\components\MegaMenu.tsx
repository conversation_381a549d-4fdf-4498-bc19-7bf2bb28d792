'use client';

import Link from 'next/link';
import Image from 'next/image';

interface MegaMenuProps {
  type: 'women' | 'men' | 'just-in' | 'designers' | 'clothing' | 'dresses' | 'shoes' | 'bags' | 'accessories' | 'jewellery' | 'home' | 'edits' | 'outlet';
}

const MegaMenu = ({ type }: MegaMenuProps) => {
  const womenCategories = [
    {
      title: 'Just In',
      links: [
        { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },
        { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },
        { name: 'Back in stock', href: '/womens/lists/back-in-stock' },
        { name: 'Coming soon', href: '/womens/lists/coming-soon' },
        { name: 'Exclusives', href: '/womens/lists/exclusives' },
      ]
    },
    {
      title: 'Clothing',
      links: [
        { name: 'Shop all', href: '/womens/shop/clothing' },
        { name: 'Dress<PERSON>', href: '/womens/shop/clothing/dresses' },
        { name: 'Coats', href: '/womens/shop/clothing/coats' },
        { name: 'Jackets', href: '/womens/shop/clothing/jackets' },
        { name: 'Knitwear', href: '/womens/shop/clothing/knitwear' },
        { name: 'Tops', href: '/womens/shop/clothing/tops' },
        { name: 'Trousers', href: '/womens/shop/clothing/trousers' },
        { name: 'Jeans', href: '/womens/shop/clothing/jeans' },
        { name: 'Skirts', href: '/womens/shop/clothing/skirts' },
      ]
    },
    {
      title: 'Shoes',
      links: [
        { name: 'Shop all', href: '/womens/shop/shoes' },
        { name: 'Boots', href: '/womens/shop/shoes/boots' },
        { name: 'Heels', href: '/womens/shop/shoes/heels' },
        { name: 'Flats', href: '/womens/shop/shoes/flats' },
        { name: 'Trainers', href: '/womens/shop/shoes/sneakers' },
        { name: 'Sandals', href: '/womens/shop/shoes/sandals' },
      ]
    },
    {
      title: 'Bags',
      links: [
        { name: 'Shop all', href: '/womens/shop/bags' },
        { name: 'Tote bags', href: '/womens/shop/bags/tote-bags' },
        { name: 'Shoulder bags', href: '/womens/shop/bags/shoulder-bags' },
        { name: 'Cross-body bags', href: '/womens/shop/bags/cross-body-bags' },
        { name: 'Clutch bags', href: '/womens/shop/bags/clutch-bags' },
        { name: 'Mini bags', href: '/womens/shop/bags/mini-bags' },
      ]
    },
    {
      title: 'Accessories',
      links: [
        { name: 'View all', href: '/womens/shop/accessories' },
        { name: 'Jewellery', href: '/womens/shop/jewellery-and-watches' },
        { name: 'Sunglasses', href: '/womens/shop/accessories/sunglasses' },
        { name: 'Belts', href: '/womens/shop/accessories/belts' },
        { name: 'Scarves', href: '/womens/shop/accessories/scarves' },
        { name: 'Hats', href: '/womens/shop/accessories/hats' },
      ]
    }
  ];

  const menCategories = [
    {
      title: 'Just In',
      links: [
        { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },
        { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },
        { name: 'Back in stock', href: '/mens/lists/back-in-stock' },
        { name: 'Coming soon', href: '/mens/lists/coming-soon' },
        { name: 'Exclusives', href: '/mens/lists/exclusives' },
      ]
    },
    {
      title: 'Clothing',
      links: [
        { name: 'Shop all', href: '/mens/shop/clothing' },
        { name: 'Suits', href: '/mens/shop/clothing/suits' },
        { name: 'Blazers', href: '/mens/shop/clothing/blazers' },
        { name: 'Coats', href: '/mens/shop/clothing/coats' },
        { name: 'Knitwear', href: '/mens/shop/clothing/knitwear' },
        { name: 'Shirts', href: '/mens/shop/clothing/casual-shirts' },
        { name: 'T-shirts', href: '/mens/shop/clothing/t-shirts' },
        { name: 'Trousers', href: '/mens/shop/clothing/trousers' },
        { name: 'Jeans', href: '/mens/shop/clothing/jeans' },
      ]
    },
    {
      title: 'Shoes',
      links: [
        { name: 'Shop all', href: '/mens/shop/shoes' },
        { name: 'Formal shoes', href: '/mens/shop/shoes/dress-shoes' },
        { name: 'Boots', href: '/mens/shop/shoes/boots' },
        { name: 'Trainers', href: '/mens/shop/shoes/sneakers' },
        { name: 'Loafers', href: '/mens/shop/shoes/loafers' },
      ]
    },
    {
      title: 'Bags',
      links: [
        { name: 'Shop all', href: '/mens/shop/bags' },
        { name: 'Backpacks', href: '/mens/shop/bags/backpacks' },
        { name: 'Travel bags', href: '/mens/shop/bags/travel-bags' },
        { name: 'Briefcases', href: '/mens/shop/bags/briefcases' },
        { name: 'Messenger bags', href: '/mens/shop/bags/messenger-bags' },
      ]
    },
    {
      title: 'Accessories',
      links: [
        { name: 'View all', href: '/mens/shop/accessories' },
        { name: 'Watches', href: '/mens/shop/accessories/jewellery/watches' },
        { name: 'Sunglasses', href: '/mens/shop/accessories/sunglasses' },
        { name: 'Belts', href: '/mens/shop/accessories/belts' },
        { name: 'Wallets', href: '/mens/shop/accessories/wallets' },
        { name: 'Ties', href: '/mens/shop/accessories/ties' },
      ]
    }
  ];

  // Define categories for different menu types
  const getMenuContent = () => {
    switch (type) {
      case 'women':
        return {
          categories: womenCategories,
          designers: ['Gucci', 'Saint Laurent', 'Bottega Veneta', 'The Row', 'Khaite', 'Toteme'],
          showProducts: true
        };
      case 'men':
        return {
          categories: menCategories,
          designers: ['Tom Ford', 'Brunello Cucinelli', 'Stone Island', 'Thom Browne', 'Bottega Veneta', 'Gucci'],
          showProducts: true
        };
      case 'just-in':
        return {
          categories: [
            {
              title: 'Women',
              links: [
                { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },
                { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },
                { name: 'Back in stock', href: '/womens/lists/back-in-stock' },
                { name: 'Coming soon', href: '/womens/lists/coming-soon' },
              ]
            },
            {
              title: 'Men',
              links: [
                { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },
                { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },
                { name: 'Back in stock', href: '/mens/lists/back-in-stock' },
                { name: 'Coming soon', href: '/mens/lists/coming-soon' },
              ]
            },
            {
              title: 'Categories',
              links: [
                { name: 'Clothing', href: '/just-in/clothing' },
                { name: 'Shoes', href: '/just-in/shoes' },
                { name: 'Bags', href: '/just-in/bags' },
                { name: 'Accessories', href: '/just-in/accessories' },
              ]
            }
          ],
          designers: ['New Arrivals', 'Trending Now', 'Editor\'s Picks'],
          showProducts: true
        };
      case 'designers':
        return {
          categories: [
            {
              title: 'A-D',
              links: [
                { name: 'Acne Studios', href: '/designers/acne-studios' },
                { name: 'Bottega Veneta', href: '/designers/bottega-veneta' },
                { name: 'Celine', href: '/designers/celine' },
                { name: 'Dior', href: '/designers/dior' },
              ]
            },
            {
              title: 'E-H',
              links: [
                { name: 'Fendi', href: '/designers/fendi' },
                { name: 'Gucci', href: '/designers/gucci' },
                { name: 'Hermes', href: '/designers/hermes' },
              ]
            },
            {
              title: 'I-M',
              links: [
                { name: 'Jacquemus', href: '/designers/jacquemus' },
                { name: 'Khaite', href: '/designers/khaite' },
                { name: 'Loewe', href: '/designers/loewe' },
                { name: 'Moncler', href: '/designers/moncler' },
              ]
            },
            {
              title: 'N-S',
              links: [
                { name: 'Off-White', href: '/designers/off-white' },
                { name: 'Prada', href: '/designers/prada' },
                { name: 'Saint Laurent', href: '/designers/saint-laurent' },
              ]
            },
            {
              title: 'T-Z',
              links: [
                { name: 'The Row', href: '/designers/the-row' },
                { name: 'Toteme', href: '/designers/toteme' },
                { name: 'Valentino', href: '/designers/valentino' },
                { name: 'Zimmermann', href: '/designers/zimmermann' },
              ]
            }
          ],
          designers: ['Featured Designers', 'New Designers', 'Luxury Brands'],
          showProducts: true
        };
      default:
        return {
          categories: [
            {
              title: 'Women',
              links: [
                { name: 'Shop all', href: `/womens/${type}` },
                { name: 'New arrivals', href: `/womens/${type}/new` },
                { name: 'Best sellers', href: `/womens/${type}/best-sellers` },
              ]
            },
            {
              title: 'Men',
              links: [
                { name: 'Shop all', href: `/mens/${type}` },
                { name: 'New arrivals', href: `/mens/${type}/new` },
                { name: 'Best sellers', href: `/mens/${type}/best-sellers` },
              ]
            }
          ],
          designers: ['Featured Brands', 'New Arrivals', 'Best Sellers'],
          showProducts: false
        };
    }
  };

  const menuContent = getMenuContent();
  const { categories, designers: featuredDesigners, showProducts } = menuContent;

  // Calculate grid columns based on content
  const totalColumns = categories.length + 1; // +1 for featured designers
  const gridCols = totalColumns <= 3 ? 'grid-cols-3' :
                   totalColumns <= 4 ? 'grid-cols-4' :
                   totalColumns <= 5 ? 'grid-cols-5' : 'grid-cols-6';

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
        <div className={`grid ${gridCols} gap-8`}>
          {/* Categories */}
          {categories.map((category, index) => (
            <div key={index} className="space-y-4">
              <h3 className="font-semibold text-sm uppercase tracking-wide text-gray-900">
                {category.title}
              </h3>
              <ul className="space-y-2">
                {category.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-sm text-gray-600 hover:text-black transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* Featured Designers */}
          <div className="space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wide text-gray-900">
              Featured {type === 'designers' ? 'Collections' : 'Designers'}
            </h3>
            <ul className="space-y-2">
              {featuredDesigners.map((designer, index) => (
                <li key={index}>
                  <Link
                    href={`/${type}/designers/${designer.toLowerCase().replace(/\s+/g, '-')}`}
                    className="text-sm text-gray-600 hover:text-black transition-colors"
                  >
                    {designer}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Featured Products Section - Only show for certain menu types */}
        {showProducts && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="grid grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="group cursor-pointer">
                  <div className="aspect-square bg-gray-100 mb-3 overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                      <span className="text-gray-500 text-sm">Product Image</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-gray-500 uppercase tracking-wide">Designer Name</p>
                    <p className="text-sm font-medium">Product Name</p>
                    <p className="text-sm font-semibold">£XXX</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

export default MegaMenu;
