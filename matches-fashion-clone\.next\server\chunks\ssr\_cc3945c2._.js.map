{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Heart, ShoppingBag } from 'lucide-react';\n\ninterface ProductCardProps {\n  id: number;\n  name: string;\n  brand: string;\n  price: number;\n  originalPrice?: number;\n  isOnSale?: boolean;\n  image: string;\n  category: string;\n  href: string;\n  className?: string;\n}\n\nconst ProductCard = ({\n  id,\n  name,\n  brand,\n  price,\n  originalPrice,\n  isOnSale = false,\n  image,\n  category,\n  href,\n  className = ''\n}: ProductCardProps) => {\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleWishlistToggle = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n  };\n\n  const handleAddToBag = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    // Add to bag logic here\n    console.log('Added to bag:', { id, name, brand, price });\n  };\n\n  return (\n    <Link href={href} className={`group cursor-pointer block ${className}`}>\n      <div \n        className=\"relative\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n      >\n        {/* Product Image */}\n        <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 mb-4 overflow-hidden relative\">\n          <div className=\"w-full h-full flex items-center justify-center\">\n            <span className=\"text-gray-500 text-sm\">Product Image</span>\n          </div>\n          \n          {/* Sale Badge */}\n          {isOnSale && (\n            <div className=\"absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 uppercase tracking-wide\">\n              Sale\n            </div>\n          )}\n\n          {/* Hover Actions */}\n          {isHovered && (\n            <div className=\"absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center\">\n              <div className=\"flex space-x-3\">\n                <button\n                  onClick={handleWishlistToggle}\n                  className={`p-3 rounded-full transition-all duration-300 ${\n                    isWishlisted \n                      ? 'bg-red-500 text-white' \n                      : 'bg-white text-black hover:bg-gray-100'\n                  }`}\n                  aria-label=\"Add to wishlist\"\n                >\n                  <Heart size={20} fill={isWishlisted ? 'currentColor' : 'none'} />\n                </button>\n                <button\n                  onClick={handleAddToBag}\n                  className=\"p-3 bg-black text-white rounded-full hover:bg-gray-800 transition-all duration-300\"\n                  aria-label=\"Add to bag\"\n                >\n                  <ShoppingBag size={20} />\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Product Info */}\n        <div className=\"space-y-1\">\n          <p className=\"text-xs text-gray-500 uppercase tracking-wide\">{brand}</p>\n          <h3 className=\"text-sm font-medium group-hover:text-gray-600 transition-colors line-clamp-2\">\n            {name}\n          </h3>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm font-semibold\">£{price}</span>\n            {isOnSale && originalPrice && (\n              <span className=\"text-sm text-gray-500 line-through\">£{originalPrice}</span>\n            )}\n          </div>\n          <p className=\"text-xs text-gray-500\">{category}</p>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAmBA,MAAM,cAAc,CAAC,EACnB,EAAE,EACF,IAAI,EACJ,KAAK,EACL,KAAK,EACL,aAAa,EACb,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB,CAAC;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,wBAAwB;QACxB,QAAQ,GAAG,CAAC,iBAAiB;YAAE;YAAI;YAAM;YAAO;QAAM;IACxD;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW,CAAC,2BAA2B,EAAE,WAAW;kBACpE,cAAA,8OAAC;YACC,WAAU;YACV,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;;8BAGjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;wBAIzC,0BACC,8OAAC;4BAAI,WAAU;sCAAwF;;;;;;wBAMxG,2BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,6CAA6C,EACvD,eACI,0BACA,yCACJ;wCACF,cAAW;kDAEX,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,MAAM,eAAe,iBAAiB;;;;;;;;;;;kDAEzD,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ7B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAiD;;;;;;sCAC9D,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAEH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAwB;wCAAE;;;;;;;gCACzC,YAAY,+BACX,8OAAC;oCAAK,WAAU;;wCAAqC;wCAAE;;;;;;;;;;;;;sCAG3D,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAKhD;uCAEe", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/mens/shop/clothing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Filter, Grid, List, ChevronDown } from 'lucide-react';\nimport ProductCard from '@/components/ProductCard';\n\nexport default function MensClothingPage() {\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [sortBy, setSortBy] = useState('newest');\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Mock product data\n  const products = Array.from({ length: 24 }, (_, i) => ({\n    id: i + 1,\n    name: `Designer Item ${i + 1}`,\n    brand: ['Tom Ford', 'Brunello Cucinelli', 'Stone Island', 'Thom Browne', 'Bottega Veneta'][i % 5],\n    price: Math.floor(Math.random() * 3000) + 300,\n    originalPrice: Math.floor(Math.random() * 3000) + 300,\n    isOnSale: Math.random() > 0.7,\n    image: `product-${i + 1}`,\n    category: ['Suits', 'Blazers', 'Shirts', 'Trousers', 'Knitwear'][i % 5],\n  }));\n\n  const categories = [\n    'Shop all',\n    'Activewear',\n    'Blazers',\n    'Coats',\n    'Denim',\n    'Jackets',\n    'Jeans',\n    'Knitwear',\n    'Loungewear',\n    'Polo shirts',\n    'Shirts',\n    'Shorts',\n    'Suits',\n    'Swimwear',\n    'T-shirts',\n    'Trousers',\n    'Underwear & nightwear',\n  ];\n\n  const designers = [\n    'Brunello Cucinelli',\n    'Bottega Veneta',\n    'Gucci',\n    'Saint Laurent',\n    'Stone Island',\n    'Thom Browne',\n    'Tom Ford',\n    'Valentino',\n    'Balenciaga',\n    'Prada',\n    'Ermenegildo Zegna',\n    'Loro Piana',\n    'Kiton',\n    'Brioni',\n    'Canali',\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 py-4\">\n        <nav className=\"text-sm text-gray-500\">\n          <Link href=\"/\" className=\"hover:text-black\">Home</Link>\n          <span className=\"mx-2\">/</span>\n          <Link href=\"/mens\" className=\"hover:text-black\">Men</Link>\n          <span className=\"mx-2\">/</span>\n          <span className=\"text-black\">Clothing</span>\n        </nav>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex\">\n          {/* Sidebar Filters */}\n          <aside className={`w-64 pr-8 ${showFilters ? 'block' : 'hidden'} lg:block`}>\n            <div className=\"space-y-8\">\n              {/* Categories */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Clothing</h3>\n                <ul className=\"space-y-2\">\n                  {categories.map((category, index) => (\n                    <li key={index}>\n                      <Link\n                        href={`/mens/shop/clothing/${category.toLowerCase().replace(/\\s+/g, '-')}`}\n                        className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                      >\n                        {category}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Designers */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Designers</h3>\n                <ul className=\"space-y-2\">\n                  {designers.slice(0, 10).map((designer, index) => (\n                    <li key={index}>\n                      <Link\n                        href={`/mens/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`}\n                        className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                      >\n                        {designer}\n                      </Link>\n                    </li>\n                  ))}\n                  <li>\n                    <button className=\"text-sm text-gray-600 hover:text-black transition-colors\">\n                      View all designers\n                    </button>\n                  </li>\n                </ul>\n              </div>\n\n              {/* Size Filter */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Size</h3>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  {['XS', 'S', 'M', 'L', 'XL', 'XXL'].map((size) => (\n                    <button\n                      key={size}\n                      className=\"border border-gray-300 py-2 text-sm hover:border-black transition-colors\"\n                    >\n                      {size}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Suit Size Filter */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Suit Size</h3>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {['36', '38', '40', '42', '44', '46', '48', '50'].map((size) => (\n                    <button\n                      key={size}\n                      className=\"border border-gray-300 py-2 text-sm hover:border-black transition-colors\"\n                    >\n                      {size}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Price Filter */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Price</h3>\n                <div className=\"space-y-2\">\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"10000\"\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-sm text-gray-600\">\n                    <span>£0</span>\n                    <span>£10000+</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </aside>\n\n          {/* Main Content */}\n          <main className=\"flex-1\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-8\">\n              <div>\n                <h1 className=\"text-3xl font-bold mb-2\">Men's Clothing</h1>\n                <p className=\"text-gray-600\">{products.length} items</p>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                {/* Mobile Filter Toggle */}\n                <button\n                  className=\"lg:hidden flex items-center space-x-2 px-4 py-2 border border-gray-300 hover:border-black\"\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <Filter size={16} />\n                  <span className=\"text-sm\">Filters</span>\n                </button>\n\n                {/* Sort Dropdown */}\n                <div className=\"relative\">\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm hover:border-black focus:outline-none focus:border-black\"\n                  >\n                    <option value=\"newest\">Newest</option>\n                    <option value=\"price-low\">Price: Low to High</option>\n                    <option value=\"price-high\">Price: High to Low</option>\n                    <option value=\"popular\">Most Popular</option>\n                  </select>\n                  <ChevronDown size={16} className=\"absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none\" />\n                </div>\n\n                {/* View Mode Toggle */}\n                <div className=\"flex border border-gray-300\">\n                  <button\n                    className={`p-2 ${viewMode === 'grid' ? 'bg-black text-white' : 'bg-white text-black'}`}\n                    onClick={() => setViewMode('grid')}\n                  >\n                    <Grid size={16} />\n                  </button>\n                  <button\n                    className={`p-2 ${viewMode === 'list' ? 'bg-black text-white' : 'bg-white text-black'}`}\n                    onClick={() => setViewMode('list')}\n                  >\n                    <List size={16} />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Product Grid */}\n            <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>\n              {products.map((product) => (\n                <ProductCard\n                  key={product.id}\n                  id={product.id}\n                  name={product.name}\n                  brand={product.brand}\n                  price={product.price}\n                  originalPrice={product.originalPrice}\n                  isOnSale={product.isOnSale}\n                  image={product.image}\n                  category={product.category}\n                  href={`/mens/product/${product.id}`}\n                />\n              ))}\n            </div>\n\n            {/* Load More */}\n            <div className=\"text-center mt-12\">\n              <button className=\"luxury-button-outline\">\n                Load More\n              </button>\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oBAAoB;IACpB,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC;YACrD,IAAI,IAAI;YACR,MAAM,CAAC,cAAc,EAAE,IAAI,GAAG;YAC9B,OAAO;gBAAC;gBAAY;gBAAsB;gBAAgB;gBAAe;aAAiB,CAAC,IAAI,EAAE;YACjG,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YAC1C,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YAClD,UAAU,KAAK,MAAM,KAAK;YAC1B,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;YACzB,UAAU;gBAAC;gBAAS;gBAAW;gBAAU;gBAAY;aAAW,CAAC,IAAI,EAAE;QACzE,CAAC;IAED,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAmB;;;;;;sCAC5C,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAQ,WAAU;sCAAmB;;;;;;sCAChD,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAa;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAM,WAAW,CAAC,UAAU,EAAE,cAAc,UAAU,SAAS,SAAS,CAAC;sCACxE,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;0DACX,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,oBAAoB,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;4DAC1E,WAAU;sEAET;;;;;;uDALI;;;;;;;;;;;;;;;;kDAaf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;;oDACX,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,sBACrC,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,gBAAgB,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;gEACtE,WAAU;0EAET;;;;;;2DALI;;;;;kEASX,8OAAC;kEACC,cAAA,8OAAC;4DAAO,WAAU;sEAA2D;;;;;;;;;;;;;;;;;;;;;;;kDAQnF,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAM;oDAAK;oDAAK;oDAAK;oDAAM;iDAAM,CAAC,GAAG,CAAC,CAAC,qBACvC,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAUb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAM;oDAAM;oDAAM;oDAAM;oDAAM;oDAAM;oDAAM;iDAAK,CAAC,GAAG,CAAC,CAAC,qBACrD,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAUb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC;4BAAK,WAAU;;8CAEd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;;wDAAiB,SAAS,MAAM;wDAAC;;;;;;;;;;;;;sDAGhD,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,WAAU;oDACV,SAAS,IAAM,eAAe,CAAC;;sEAE/B,8OAAC,sMAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;sEACd,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4DACzC,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAU;;;;;;;;;;;;sEAE1B,8OAAC,oNAAA,CAAA,cAAW;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;8DAInC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,wBAAwB,uBAAuB;4DACvF,SAAS,IAAM,YAAY;sEAE3B,cAAA,8OAAC,yMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;sEAEd,8OAAC;4DACC,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,wBAAwB,uBAAuB;4DACvF,SAAS,IAAM,YAAY;sEAE3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;oCAAI,WAAW,CAAC,WAAW,EAAE,aAAa,SAAS,8CAA8C,eAAe;8CAC9G,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,iIAAA,CAAA,UAAW;4CAEV,IAAI,QAAQ,EAAE;4CACd,MAAM,QAAQ,IAAI;4CAClB,OAAO,QAAQ,KAAK;4CACpB,OAAO,QAAQ,KAAK;4CACpB,eAAe,QAAQ,aAAa;4CACpC,UAAU,QAAQ,QAAQ;4CAC1B,OAAO,QAAQ,KAAK;4CACpB,UAAU,QAAQ,QAAQ;4CAC1B,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;2CAT9B,QAAQ,EAAE;;;;;;;;;;8CAerB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "file": "grid-3x3.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/grid-3x3.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M3 9h18', key: '1pudct' }],\n  ['path', { d: 'M3 15h18', key: '5xshup' }],\n  ['path', { d: 'M9 3v18', key: 'fh3hqa' }],\n  ['path', { d: 'M15 3v18', key: '14nvp0' }],\n];\n\n/**\n * @component @name Grid3x3\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDloMTgiIC8+CiAgPHBhdGggZD0iTTMgMTVoMTgiIC8+CiAgPHBhdGggZD0iTTkgM3YxOCIgLz4KICA8cGF0aCBkPSJNMTUgM3YxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/grid-3x3\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Grid3x3 = createLucideIcon('grid-3x3', __iconNode);\n\nexport default Grid3x3;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "file": "list.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/list.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12h.01', key: 'nlz23k' }],\n  ['path', { d: 'M3 18h.01', key: '1tta3j' }],\n  ['path', { d: 'M3 6h.01', key: '1rqtza' }],\n  ['path', { d: 'M8 12h13', key: '1za7za' }],\n  ['path', { d: 'M8 18h13', key: '1lx6n3' }],\n  ['path', { d: 'M8 6h13', key: 'ik3vkj' }],\n];\n\n/**\n * @component @name List\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmguMDEiIC8+CiAgPHBhdGggZD0iTTMgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0zIDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDEzIiAvPgogIDxwYXRoIGQ9Ik04IDE4aDEzIiAvPgogIDxwYXRoIGQ9Ik04IDZoMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/list\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst List = createLucideIcon('list', __iconNode);\n\nexport default List;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "file": "chevron-down.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,cAAgB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa7E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}