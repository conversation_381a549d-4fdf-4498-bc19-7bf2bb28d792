'use client';

import { useState } from 'react';
import <PERSON> from 'next/link';
import { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';
import MegaMenu from './MegaMenu';
import SearchModal from './SearchModal';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuHover = (menu: string) => {
    setActiveMenu(menu);
  };

  const handleMenuLeave = () => {
    setActiveMenu(null);
  };

  return (
    <header className="relative bg-white border-b border-gray-200">
      {/* Top Bar - Hidden on mobile */}
      <div className="hidden md:block bg-black text-white text-xs py-2">
        <div className="max-w-7xl mx-auto px-4 flex justify-between items-center">
          <div className="flex space-x-6">
            <Link href="/help" className="hover:underline">Help Centre</Link>
            <Link href="/delivery" className="hover:underline">Delivery</Link>
            <Link href="/returns" className="hover:underline">Returns</Link>
          </div>
          <div className="flex space-x-6">
            <Link href="/stores" className="hover:underline">Visit Us</Link>
            <Link href="/apps" className="hover:underline">Our Apps</Link>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div
        className="max-w-7xl mx-auto px-4 relative"
        onMouseLeave={handleMenuLeave}
      >
        <div className="flex items-center h-16 md:h-20">
          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 mr-4"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>

          {/* Logo */}
          <Link href="/" className="flex-shrink-0 mr-8">
            <span className="text-2xl md:text-3xl font-bold tracking-wider">MATCHES</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center flex-1">
            {/* Primary Categories - Men & Women */}
            <div className="flex space-x-8 mr-12">
              <div
                onMouseEnter={() => handleMenuHover('women')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/womens"
                  className="text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Women
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('men')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/mens"
                  className="text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Men
                </Link>
              </div>
            </div>

            {/* Secondary Categories */}
            <div className="flex space-x-6 flex-1">
              <div
                onMouseEnter={() => handleMenuHover('just-in')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/just-in"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Just In
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('designers')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/designers"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Designers
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('clothing')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/clothing"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Clothing
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('dresses')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/dresses"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Dresses
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('shoes')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/shoes"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Shoes
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('bags')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/bags"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Bags
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('accessories')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/accessories"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Accessories
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('jewellery')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/jewellery-watches"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Jewellery & Watches
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('home')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/home"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Home
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('edits')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/edits"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Edits
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('outlet')}
                onMouseLeave={handleMenuLeave}
              >
                <Link
                  href="/outlet"
                  className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Outlet
                </Link>
              </div>
            </div>
          </nav>

          {/* Right Icons */}
          <div className="flex items-center space-x-4 ml-auto">
            <button
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              onClick={() => setIsSearchOpen(true)}
            >
              <Search size={20} />
            </button>
            <button className="hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors">
              <User size={20} />
            </button>
            <button className="hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Settings size={20} />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <Heart size={20} />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <ShoppingBag size={20} />
              <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>
          </div>
        </div>

        {/* Unified Mega Menu Container - positioned relative to header container */}
        {activeMenu && (
          <div className="absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-200 z-50">
            <MegaMenu type={activeMenu as any} />
          </div>
        )}
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={toggleMobileMenu} />
          <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <span className="text-xl font-bold">MATCHES</span>
              <button onClick={toggleMobileMenu}>
                <X size={24} />
              </button>
            </div>
            <nav className="p-4 overflow-y-auto">
              <div className="space-y-4">
                {/* Primary Categories */}
                <div className="space-y-2">
                  <Link
                    href="/womens"
                    className="block text-lg font-medium py-2 border-b border-gray-100"
                    onClick={toggleMobileMenu}
                  >
                    Women
                  </Link>
                  <Link
                    href="/mens"
                    className="block text-lg font-medium py-2 border-b border-gray-100"
                    onClick={toggleMobileMenu}
                  >
                    Men
                  </Link>
                </div>

                {/* Secondary Categories */}
                <div className="pt-4 space-y-2">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2">Shop</h3>
                  <Link href="/just-in" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Just In</Link>
                  <Link href="/designers" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Designers</Link>
                  <Link href="/clothing" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Clothing</Link>
                  <Link href="/dresses" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Dresses</Link>
                  <Link href="/shoes" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Shoes</Link>
                  <Link href="/bags" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Bags</Link>
                  <Link href="/accessories" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Accessories</Link>
                  <Link href="/jewellery-watches" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Jewellery & Watches</Link>
                  <Link href="/home" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Home</Link>
                  <Link href="/edits" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Edits</Link>
                  <Link href="/outlet" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Outlet</Link>
                </div>

                {/* Account & Support */}
                <div className="pt-4 space-y-2 border-t border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2">Account</h3>
                  <Link href="/account" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>My Account</Link>
                  <Link href="/settings" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Settings</Link>
                  <Link href="/help" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Help Centre</Link>
                  <Link href="/delivery" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Delivery</Link>
                </div>
              </div>
            </nav>
          </div>
        </div>
      )}

      {/* Search Modal */}
      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </header>
  );
};

export default Header;
